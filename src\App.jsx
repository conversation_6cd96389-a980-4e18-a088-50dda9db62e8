import React, { Suspense } from 'react'
import StoryForm from './components/StoryForm'
import CommunityStats from './components/CommunityStats'
import ScrollToTop, { showToast } from './components/ScrollToTop'
import SearchAndFilters from './components/SearchAndFilters'
import StoryOfTheMonth from './components/StoryOfTheMonth'
import ConfettiCelebration from './components/ConfettiCelebration'
import ProgressTracker from './components/ProgressTracker'
import Footer from './components/Footer'
import LandingPage from './components/LandingPage'
import GuestNotification from './components/GuestNotification'

import { addChangelogEntries } from './utils/addChangelogEntries'
import { firebaseListenerManager } from './utils/performance'
import { checkDeploymentEnvironment, logDeploymentInfo } from './utils/deploymentCheck'
import { useState, useEffect, useMemo, useCallback } from 'react'

// Lazy load heavy components for better performance
const EconomicEmpowerment = React.lazy(() => import('./components/EconomicEmpowerment'))
const CommunityDialogue = React.lazy(() => import('./components/CommunityDialogue'))
const CommunitySupport = React.lazy(() => import('./components/CommunitySupport'))
const CommunityActivism = React.lazy(() => import('./components/CommunityActivism'))
const TaskManagementDashboard = React.lazy(() => import('./components/TaskManagementDashboard'))
const MessagingSystem = React.lazy(() => import('./components/MessagingSystem'))
const NotificationCenter = React.lazy(() => import('./components/NotificationCenter'))
const DynamicNewsfeed = React.lazy(() => import('./components/DynamicNewsfeed'))
const HomeScreen = React.lazy(() => import('./components/HomeScreen'))
const Changelog = React.lazy(() => import('./components/Changelog'))
const FriendsManager = React.lazy(() => import('./components/FriendsManager'))
const FriendRequestModal = React.lazy(() => import('./components/FriendRequestModal'))
const PrivacySettings = React.lazy(() => import('./components/PrivacySettings'))
const AdminDashboard = React.lazy(() => import('./components/AdminDashboard'))


import './App.css'
import './auth.css'
import { useAuth } from './AuthContext'
import { saveStory, getStoriesRealtime, reactToStory, voteForStoryOfMonth, updateStory, deleteStory } from './services/stories'
import { initializeAdminSystem, getUserRole, ADMIN_ROLES } from './services/admin'
// Auth components are imported in LandingPage component
import { Link } from 'react-router-dom'

const FEATURED_QUOTES = [
  '"If you want to go fast, go alone. If you want to go far, go together." – African Proverb',
  '"The time is always right to do what is right." – Martin Luther King Jr.',
  '"Success is to be measured not so much by the position that one has reached in life as by the obstacles which he has overcome." – Booker T. Washington',
  '"Never be limited by other people\'s limited imagination." – Mae Jemison',
  '"If you don\'t like something, change it. If you can\'t change it, change your attitude." – Maya Angelou'
];

const RESOURCES = [
  { name: 'NAACP', url: 'https://naacp.org/' },
  { name: 'Black Girls Code', url: 'https://www.blackgirlscode.com/' },
  { name: 'National Urban League', url: 'https://nul.org/' },
  { name: 'The Conscious Kid', url: 'https://www.theconsciouskid.org/' },
];

const TOPICS = [
  'All Stories',
  'Culture & Heritage',
  'Family & Community',
  'Art & Creativity',
  'History & Legacy',
  'Education & Achievement',
  'Business & Entrepreneurship',
  'Health & Wellness',
  'Inspiration & Overcoming',
  'Activism & Leadership',
  'Joy & Celebration',
];

function App() {
  const [stories, setStories] = useState([])
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedTopic, setSelectedTopic] = useState('All Stories')
  const [featuredStory, setFeaturedStory] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [bookmarkedStories, setBookmarkedStories] = useState([])
  const [showBookmarksOnly, setShowBookmarksOnly] = useState(false)
  const [storyOfTheMonth, setStoryOfTheMonth] = useState(null)
  const votingPeriod = new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  const [showCelebration, setShowCelebration] = useState(false)
  const [darkMode, setDarkMode] = useState(false) // Fix: Define darkMode state
  const [userProfiles, setUserProfiles] = useState({})
  const { currentUser: authUser, isGuestMode, logout, fetchUserProfile, exitGuestMode } = useAuth();
  const [userProfile, setUserProfile] = useState(null);
  const [currentView, setCurrentView] = useState('home'); // 'home', 'stories', 'economic', 'dialogue', 'support', 'activism'
  const [showTaskManagement, setShowTaskManagement] = useState(false);
  const [showMessaging, setShowMessaging] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showNewsfeed, setShowNewsfeed] = useState(false);
  const [showChangelog, setShowChangelog] = useState(false);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const [storyFormClearFunction, setStoryFormClearFunction] = useState(null);
  const [showFriendsManager, setShowFriendsManager] = useState(false);
  const [showFriendRequestModal, setShowFriendRequestModal] = useState(false);
  const [friendRequestData, setFriendRequestData] = useState(null);
  const [showPrivacySettings, setShowPrivacySettings] = useState(false);
  const [showAdminDashboard, setShowAdminDashboard] = useState(false);


  const [userRole, setUserRole] = useState(null);

  // Debug authentication state
  useEffect(() => {
    console.log('Auth user changed:', authUser ? authUser.uid : 'No user');
  }, [authUser])

  // Debug logging for modal states
  useEffect(() => {
    console.log('Modal states:', {
      showMessaging,
      showNotifications,
      showTaskManagement,
      showNewsfeed,
      showChangelog
    });
  }, [showMessaging, showNotifications, showTaskManagement, showNewsfeed, showChangelog]);



  useEffect(() => {
    async function loadProfile() {
      if (authUser) {
        const profile = await fetchUserProfile(authUser.uid);
        setUserProfile(profile);

        // Initialize admin system and get user role
        try {
          await initializeAdminSystem(authUser.uid, authUser.email);
          const role = await getUserRole(authUser.uid);
          setUserRole(role);
        } catch (error) {
          console.error('Error initializing admin system:', error);
        }
      }
    }
    loadProfile();
  }, [authUser, fetchUserProfile])

  // Add changelog entries on app load (runs once)
  useEffect(() => {
    async function addChangelogEntries() {
      try {
        // Run deployment checks first
        console.log('🔍 Running deployment environment checks...');
        logDeploymentInfo();
        const hasFirebaseConfig = checkDeploymentEnvironment();

        if (!hasFirebaseConfig) {
          console.error('❌ Firebase configuration missing - some features may not work');
          return;
        }
        // Add v1.1.1 changelog if needed
        const { addV111ChangelogIfNeeded } = await import('./utils/addCriticalFixesChangelog.js');
        const v111Result = await addV111ChangelogIfNeeded();
        if (v111Result.success) {
          console.log('✅ v1.1.1 changelog check completed');
        }

        // Add v1.1.2 changelog if needed
        const { addV112ChangelogIfNeeded } = await import('./utils/addV112Changelog.js');
        const v112Result = await addV112ChangelogIfNeeded();
        if (v112Result.success) {
          console.log('✅ v1.1.2 changelog check completed');
        }

        // Add v1.2.0 changelog if needed
        const { addV120Changelog } = await import('./utils/addV120Changelog.js');
        const v120Result = await addV120Changelog();
        if (v120Result.success) {
          console.log('✅ v1.2.0 changelog check completed');
        }
      } catch (error) {
        console.log('ℹ️ Changelog update skipped (normal in development):', error.message);
      }
    }

    // Only run in production or when Firebase is properly configured
    if (!import.meta.env.DEV || import.meta.env.VITE_FIREBASE_PROJECT_ID) {
      addChangelogEntries();
    }
  }, []) // Empty dependency array - runs once on mount



  // Rotate quotes every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuoteIndex((prev) => (prev + 1) % FEATURED_QUOTES.length)
    }, 8000)
    return () => clearInterval(interval)
  }, [])

  // Load stories from Firebase with real-time updates
  useEffect(() => {
    if (!authUser && !isGuestMode) {
      console.log('No authenticated user and not in guest mode, skipping stories listener setup');
      return;
    }

    console.log('Setting up stories listener for:', authUser ? `user: ${authUser.uid}` : 'guest user');

    // Optimize Firebase listener with debouncing and error handling
    const optimizedCallback = async (loadedStories) => {
      console.log('Stories loaded via listener:', loadedStories.length);
      setStories(loadedStories);

      // Load user profiles for story authors
      const authorIds = [...new Set(loadedStories.map(story => story.author))];
      const profiles = {};

      for (const authorId of authorIds) {
        if (authorId && !userProfiles[authorId]) {
          try {
            const profile = await fetchUserProfile(authorId);
            if (profile) {
              profiles[authorId] = profile;
            }
          } catch (error) {
            console.log('Could not load profile for author:', authorId);
          }
        }
      }

      if (Object.keys(profiles).length > 0) {
        setUserProfiles(prev => ({ ...prev, ...profiles }));
      }
    };

    const unsubscribe = getStoriesRealtime(optimizedCallback, {
      onError: (error) => {
        console.error('Stories listener error:', error);
        firebaseListenerManager.handleConnectionError(error, 'stories');
      }
    });

    // Register listener with manager for cleanup
    firebaseListenerManager.addListener('stories', unsubscribe);

    // Make addChangelogEntries available globally for console access
    window.addChangelogEntries = addChangelogEntries;

    // Add debug function to manually check stories
    window.debugStories = async () => {
      try {
        const { collection, getDocs } = await import('firebase/firestore');
        const { db } = await import('./firebase');
        console.log('Testing Firebase connection...');
        const storiesSnapshot = await getDocs(collection(db, 'stories'));
        console.log('Manual stories fetch:', storiesSnapshot.docs.length, 'stories found');
        storiesSnapshot.docs.forEach(doc => {
          console.log('Story:', doc.id, doc.data());
        });
        return storiesSnapshot.docs.length;
      } catch (error) {
        console.error('Error fetching stories manually:', error);
        return -1;
      }
    };

    // Add debug function to check auth state
    window.debugAuth = () => {
      console.log('Current auth user:', authUser);
      console.log('Auth user UID:', authUser?.uid);
      console.log('Auth user email:', authUser?.email);
    };

    return () => {
      firebaseListenerManager.removeListener('stories');
    }
  }, [authUser, isGuestMode])

  // Memoize featured story calculation to avoid recalculation on every render
  const featuredStoryMemo = useMemo(() => {
    if (stories.length === 0) return null;

    // Find story with most reactions or latest story
    return stories.reduce((prev, current) =>
      (prev.hearts + prev.claps) > (current.hearts + current.claps) ? prev : current
    );
  }, [stories]);

  // Update featured story when memoized value changes
  useEffect(() => {
    setFeaturedStory(featuredStoryMemo);
  }, [featuredStoryMemo])

  // Update Story of the Month
  useEffect(() => {
    if (stories.length > 0) {
      const previousWinner = storyOfTheMonth;
      const monthlyCandidate = stories
        .filter(story => story.monthlyVotes > 0)
        .sort((a, b) => b.monthlyVotes - a.monthlyVotes)[0]
      
      if (monthlyCandidate && (!previousWinner || monthlyCandidate.id !== previousWinner.id)) {
        setStoryOfTheMonth(monthlyCandidate)
        if (previousWinner) {
          setShowCelebration(true)
        }
      }
    }
  }, [stories, storyOfTheMonth])

  const handleStorySubmit = useCallback(async (story) => {
    // Enhanced authentication checks
    if (!authUser) {
      if (isGuestMode) {
        showToast('📝 Sign up to share your story with the community!', 'info')
        exitGuestMode(); // This will return to landing page
      } else {
        showToast('❌ Please log in to share your story', 'error')
      }
      return
    }

    if (!authUser.uid) {
      showToast('❌ Authentication error. Please refresh the page and try again.', 'error')
      return
    }

    // Validate story data
    if (!story.title?.trim() || !story.content?.trim()) {
      showToast('❌ Please provide both a title and content for your story', 'error')
      return
    }

    setIsSubmitting(true)

    try {
      // Show saving feedback
      showToast('💾 Saving your story...', 'info')

      // Add additional validation
      const storyToSave = {
        ...story,
        title: story.title.trim(),
        content: story.content.trim(),
        tags: story.tags || []
      }

      console.log('Submitting story for user:', authUser.uid)
      const result = await saveStory(storyToSave, authUser.uid)

      if (result.success) {
        // Clear the story form
        if (storyFormClearFunction) {
          storyFormClearFunction();
        }

        // Show celebration
        setShowCelebration(true)
        setTimeout(() => setShowCelebration(false), 3000)

        // Show success toast
        showToast('🎉 Your story has been shared with the community!', 'success')

        // Refresh stories list if needed
        console.log('Story saved successfully:', result.id)
      }
    } catch (error) {
      console.error('Error saving story:', error)

      // More specific error handling
      let errorMessage = error.message
      if (errorMessage.includes('permission-denied')) {
        errorMessage = 'Permission denied. Please check your internet connection and try again.'
      } else if (errorMessage.includes('unauthenticated')) {
        errorMessage = 'Please log out and log back in, then try again.'
      }

      showToast('❌ Failed to save story: ' + errorMessage, 'error')
    } finally {
      setIsSubmitting(false)
    }
  }, [authUser, storyFormClearFunction])



  async function handleReaction(storyId, reactionType) {
    if (!authUser) {
      if (isGuestMode) {
        showToast('💝 Sign up to react to stories and connect with the community!', 'info')
        exitGuestMode(); // This will return to landing page
      } else {
        showToast('❌ Please log in to react to stories', 'error')
      }
      return
    }

    try {
      const result = await reactToStory(storyId, reactionType, authUser.uid)

      if (result.success) {
        const emoji = reactionType === 'heart' ? '❤️' : reactionType === 'clap' ? '👏' : '📤'
        const action = result.hasReacted ? 'added' : 'removed'
        showToast(`${emoji} Reaction ${action}!`, 'success')
      }
    } catch (error) {
      console.error('Error reacting to story:', error)
      showToast('❌ Failed to react: ' + error.message, 'error')
    }
  }

  function handleShare(story) {
    // Increment share count
    setStories(stories.map(s => 
      s.id === story.id 
        ? { ...s, shares: s.shares + 1 }
        : s
    ))
    
    // Copy story to clipboard for sharing
    const shareText = `Check out this inspiring story from NAROOP: "${story.title}" - ${window.location.href}`
    navigator.clipboard.writeText(shareText)
    showToast('📋 Story link copied to clipboard! Share the inspiration! ✨', 'info')
  }

  function handleBookmark(storyId) {
    if (bookmarkedStories.includes(storyId)) {
      setBookmarkedStories(bookmarkedStories.filter(id => id !== storyId))
      showToast('📖 Bookmark removed', 'info')
    } else {
      setBookmarkedStories([...bookmarkedStories, storyId])
      showToast('📚 Story bookmarked! Check your saved stories.', 'success')
    }
  }

  // Helper function to check if current user has reacted to a story
  function hasUserReacted(story, reactionType) {
    if (!authUser) return false;
    const reactionField = `${reactionType}By`; // heartBy, clapBy
    return story[reactionField] && story[reactionField].includes(authUser.uid);
  }

  async function handleMonthlyVote(storyId) {
    if (!authUser) {
      showToast('❌ Please log in to vote', 'error')
      return
    }

    try {
      const result = await voteForStoryOfMonth(storyId, authUser.uid)

      if (result.success) {
        showToast('🗳️ Vote cast for Story of the Month!', 'success')
      }
    } catch (error) {
      console.error('Error voting for story:', error)
      showToast('❌ ' + error.message, 'error')
    }
  }

  async function handleStoryUpdate(storyId, updates) {
    if (!authUser) {
      showToast('❌ Please log in to edit stories', 'error')
      return
    }

    try {
      showToast('💾 Updating story...', 'info')
      const result = await updateStory(storyId, updates, authUser.uid)

      if (result.success) {
        showToast('✅ Story updated successfully!', 'success')
      }
    } catch (error) {
      console.error('Error updating story:', error)
      showToast('❌ Failed to update story: ' + error.message, 'error')
    }
  }

  async function handleStoryDelete(storyId, storyTitle, storyContent) {
    if (!authUser) {
      showToast('❌ Please log in to delete stories', 'error')
      return
    }

    // Show confirmation dialog with story preview
    const preview = storyContent.length > 100 ? storyContent.substring(0, 100) + '...' : storyContent;
    const confirmed = window.confirm(
      `Are you sure you want to delete this story?\n\nTitle: "${storyTitle}"\nContent: "${preview}"\n\nThis action cannot be undone.`
    );

    if (!confirmed) {
      return;
    }

    try {
      showToast('🗑️ Deleting story...', 'info')
      const result = await deleteStory(storyId, authUser.uid)

      if (result.success) {
        showToast('✅ Story deleted successfully!', 'success')
      }
    } catch (error) {
      console.error('Error deleting story:', error)
      showToast('❌ Failed to delete story: ' + error.message, 'error')
    }
  }

  function handleSearch(query) {
    setSearchQuery(query)
  }

  // Dark mode toggle function
  function toggleDarkMode() {
    setDarkMode(!darkMode)
    document.body.classList.toggle('dark-mode', !darkMode)
  }

  // Handle friend request notification clicks
  const handleOpenFriendRequest = (data) => {
    setFriendRequestData(data);
    setShowFriendRequestModal(true);
    // Close notifications panel
    setShowNotifications(false);
  };

  // Handle friend request modal actions
  const handleFriendRequestHandled = (action, request) => {
    console.log(`Friend request ${action}:`, request);
    // Optionally show a toast notification or update UI
  };

  function exportStory(story) {
    const storyText = `
${story.title}
By: ${userProfiles[story.author]?.name || 'Community Member'}
Topic: ${story.topic}
Date: ${story.timestamp}
Tags: ${story.tags?.join(', ') || 'None'}

${story.content}

---
Shared on NAROOP - Narrative of Our People
${window.location.href}
    `.trim()

    const blob = new Blob([storyText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${story.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    showToast('📄 Story exported successfully!', 'success')
  }

  function printStory(story) {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      showToast('❌ Unable to open print window. Please check your popup blocker.', 'error');
      return;
    }

    const htmlContent = `
      <html>
        <head>
          <title>${story.title}</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; line-height: 1.6; }
            h1 { color: #e63946; border-bottom: 2px solid #fbbf24; padding-bottom: 10px; }
            .meta { color: #666; margin-bottom: 20px; }
            .tags { margin-top: 20px; }
            .tag { background: #fbbf24; color: #222; padding: 2px 8px; border-radius: 12px; margin-right: 5px; font-size: 0.8em; }
          </style>
        </head>
        <body>
          <h1>${story.title}</h1>
          <div class="meta">
            <p><strong>By:</strong> ${userProfiles[story.author]?.name || 'Community Member'}</p>
            <p><strong>Topic:</strong> ${story.topic}</p>
            <p><strong>Date:</strong> ${story.timestamp}</p>
            <p><strong>Reading Time:</strong> ${story.readingTime} min</p>
          </div>
          <div class="content">
            ${story.content.split('\n').map(p => `<p>${p}</p>`).join('')}
          </div>
          ${story.tags?.length ? `
            <div class="tags">
              <strong>Tags:</strong> ${story.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
          ` : ''}
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ccc; text-align: center; color: #666;">
            <p>Shared on NAROOP - Narrative of Our People</p>
          </div>
        </body>
      </html>
    `;

    // Use modern approach instead of deprecated document.write
    printWindow.document.documentElement.innerHTML = htmlContent;
    printWindow.print();
  }

  // Advanced filtering and search
  const getFilteredAndSortedStories = () => {
    let filtered = stories;

    // Filter by topic
    if (selectedTopic !== 'All Stories') {
      filtered = filtered.filter(story => story.topic === selectedTopic)
    }

    // Filter by bookmarks if enabled
    if (showBookmarksOnly) {
      filtered = filtered.filter(story => bookmarkedStories.includes(story.id))
    }

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(story =>
        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (story.tags && story.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())))
      )
    }

    // Sort
    switch (sortBy) {
      case 'newest':
        return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      case 'oldest':
        return filtered.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
      case 'mostLoved':
        return filtered.sort((a, b) => (b.hearts + b.claps) - (a.hearts + a.claps))
      case 'mostShared':
        return filtered.sort((a, b) => b.shares - a.shares)
      case 'monthlyVotes':
        return filtered.sort((a, b) => b.monthlyVotes - a.monthlyVotes)
      default:
        return filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    }
  }

  const filteredStories = getFilteredAndSortedStories()



  if (!authUser && !isGuestMode) {
    return <LandingPage />;
  }



  try {
    return (
      <main className={`naroop-main ${darkMode ? 'dark-mode' : ''}`} style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Guest notification bar */}
      {isGuestMode && <GuestNotification />}

      <header className="naroop-auth-header">
        {isGuestMode ? (
          <>
            <span>👋 Browsing as Guest</span>
            <div className="header-actions">
              <button
                onClick={() => exitGuestMode()}
                className="naroop-signup-btn"
                title="Sign up to unlock all features"
              >
                Sign Up
              </button>
              <button
                onClick={() => {
                  exitGuestMode();
                  // This will show the landing page where they can choose to sign in
                }}
                className="naroop-login-btn"
                title="Sign in to your account"
              >
                Sign In
              </button>
            </div>
          </>
        ) : (
          <>
            <span>Welcome, {userProfile?.name || authUser.email}</span>
            <div className="header-actions">
              {userRole && userRole.role !== ADMIN_ROLES.USER && (
                <button
                  onClick={() => setShowAdminDashboard(true)}
                  className="naroop-admin-btn"
                  title="Admin Dashboard"
                >
                  🛡️ Admin
                </button>
              )}

              <button
                onClick={() => setShowPrivacySettings(true)}
                className="naroop-privacy-btn"
                title="Privacy & Security Settings"
              >
                🔒 Privacy
              </button>
              <Link to="/account" className="naroop-account-link" aria-label="Go to your account">My Account</Link>
              <button onClick={logout} className="naroop-logout-btn">Log Out</button>
            </div>
          </>
        )}
      </header>

      <nav className="naroop-main-nav">
        {/* Mobile hamburger menu toggle - hidden on larger screens via CSS */}
        <button
          className="nav-toggle"
          onClick={() => setMobileNavOpen(!mobileNavOpen)}
          aria-label="Toggle navigation menu"
          aria-expanded={mobileNavOpen}
        >
          {mobileNavOpen ? '✕' : '☰'}
        </button>

        {/* Navigation menu */}
        <div className={`nav-menu ${mobileNavOpen ? 'open' : ''}`}>
          <button
            className={`nav-btn ${currentView === 'home' ? 'active' : ''}`}
            onClick={() => {
              setCurrentView('home');
              setMobileNavOpen(false);
            }}
          >
            🏠 Home
          </button>
          <button
            className={`nav-btn ${currentView === 'stories' ? 'active' : ''}`}
            onClick={() => {
              setCurrentView('stories');
              setMobileNavOpen(false);
            }}
          >
            📖 Stories
          </button>
          <button
            className={`nav-btn ${currentView === 'economic' ? 'active' : ''}`}
            onClick={() => {
              setCurrentView('economic');
              setMobileNavOpen(false);
            }}
          >
            💰 Economic Hub
          </button>
          <button
            className={`nav-btn ${currentView === 'dialogue' ? 'active' : ''}`}
            onClick={() => {
              setCurrentView('dialogue');
              setMobileNavOpen(false);
            }}
          >
            🗣️ Dialogue
          </button>
          <button
            className={`nav-btn ${currentView === 'support' ? 'active' : ''}`}
            onClick={() => {
              setCurrentView('support');
              setMobileNavOpen(false);
            }}
          >
            🤝 Support
          </button>
          <button
            className={`nav-btn ${currentView === 'activism' ? 'active' : ''}`}
            onClick={() => {
              setCurrentView('activism');
              setMobileNavOpen(false);
            }}
          >
            ✊🏾 Activism
          </button>
          <button
            className="nav-btn task-management-btn"
            onClick={() => {
              setShowTaskManagement(true);
              setMobileNavOpen(false);
            }}
            title="Manage your content and tasks"
          >
            🧹 Manage
          </button>
          <button
            className="nav-btn messaging-btn"
            onClick={() => {
              setShowMessaging(true);
              setMobileNavOpen(false);
            }}
            title="Private messages"
          >
            💬 Messages
          </button>
          <button
            className="nav-btn friends-btn"
            onClick={() => {
              setShowFriendsManager(true);
              setMobileNavOpen(false);
            }}
            title="Friends and connections"
          >
            👥 Friends
          </button>
          <button
            className="nav-btn notifications-btn"
            onClick={() => {
              setShowNotifications(true);
              setMobileNavOpen(false);
            }}
            title="Notifications"
          >
            🔔 Alerts
          </button>
          <button
            className="nav-btn changelog-btn"
            onClick={() => {
              setShowChangelog(true);
              setMobileNavOpen(false);
            }}
            title="Latest updates and new features - See what's been added to NAROOP"
          >
            📋 What's New
          </button>

          {/* Visual separator for Kids Zone */}
          <div className="nav-separator"></div>

          <Link
            to="/kids"
            className="nav-btn kids-nav-btn"
            onClick={() => setMobileNavOpen(false)}
          >
            🌟 Kids Zone
          </Link>
        </div>
      </nav>

      {/* Hero section and featured quote only show on home page */}
      {currentView === 'home' && (
        <>
          <section className="naroop-hero">
            <div className="naroop-hero-header">
              <div className="naroop-hero-content">
                <h1>NAROOP</h1>
                <h2>Narrative of Our People</h2>
                <p className="naroop-mission">
                  A positive space for Black voices to be heard through stories, experiences, and encouragement. Our mission is to uplift, inspire, and shine a bright light on the Black community—celebrating greatness, unity, and growth.
                </p>
              </div>
            </div>
          </section>

          <div className="naroop-featured-quote" key={currentQuoteIndex}>
            {FEATURED_QUOTES[currentQuoteIndex]}
          </div>
        </>
      )}

      {/* Render different views based on currentView state */}
      {currentView === 'home' && (
        <Suspense fallback={<div className="loading-spinner">Loading home...</div>}>
          <HomeScreen
            stories={stories}
            onStorySubmit={handleStorySubmit}
            onStoryUpdate={handleStoryUpdate}
            onStoryDelete={handleStoryDelete}
            showCelebration={showCelebration}
            setShowCelebration={setShowCelebration}
            onFormClear={setStoryFormClearFunction}
          />
        </Suspense>
      )}

      {currentView === 'stories' && (
        <>
          {/* Featured Story Section */}
          {featuredStory && (
            <section className="naroop-featured-story">
              <div className="naroop-featured-container">
                <h3>⭐ Featured Community Story</h3>
                <div className="naroop-featured-content">
                  <div className="naroop-featured-info">
                    <span className="naroop-story-topic">{featuredStory.topic}</span>
                    <h4>{featuredStory.title}</h4>
                    <p>{featuredStory.content.substring(0, 150)}...</p>
                    <div className="naroop-story-reactions">
                      <span className="naroop-reaction-display">
                        ❤️ {featuredStory.hearts} 👏 {featuredStory.claps} 📤 {featuredStory.shares}
                      </span>
                    </div>
                  </div>
                  {featuredStory.imageUrl && (
                    <div className="naroop-featured-image">
                      <img src={featuredStory.imageUrl} alt="Featured story" />
                    </div>
                  )}
                </div>
              </div>
            </section>
          )}

          <StoryOfTheMonth
            story={storyOfTheMonth}
            votingPeriod={votingPeriod}
          />
        </>
      )}

      {currentView === 'economic' && (
        <Suspense fallback={<div className="loading-spinner">Loading economic empowerment...</div>}>
          <EconomicEmpowerment />
        </Suspense>
      )}
      {currentView === 'dialogue' && (
        <Suspense fallback={<div className="loading-spinner">Loading community dialogue...</div>}>
          <CommunityDialogue />
        </Suspense>
      )}
      {currentView === 'support' && (
        <Suspense fallback={<div className="loading-spinner">Loading community support...</div>}>
          <CommunitySupport />
        </Suspense>
      )}
      {currentView === 'activism' && (
        <Suspense fallback={<div className="loading-spinner">Loading community activism...</div>}>
          <CommunityActivism />
        </Suspense>
      )}
      
      {currentView === 'stories' && (
        <div className="naroop-content">
        <div className="naroop-form-col">
          <StoryForm
            onSubmit={handleStorySubmit}
            isSubmitting={isSubmitting}
            onFormClear={setStoryFormClearFunction}
            key={stories.length} // Force re-render after successful submission
          />
          <ProgressTracker
            stories={stories}
            currentUser={authUser?.uid || 'anonymous'}
            userProfiles={userProfiles}
          />
        </div>
        <section className="naroop-stories">
          <SearchAndFilters
            searchQuery={searchQuery}
            onSearchChange={handleSearch}
            selectedTopic={selectedTopic}
            onTopicChange={setSelectedTopic}
            sortBy={sortBy}
            onSortChange={setSortBy}
            showBookmarksOnly={showBookmarksOnly}
            onBookmarksToggle={() => setShowBookmarksOnly(!showBookmarksOnly)}
            bookmarkCount={bookmarkedStories.length}
            TOPICS={TOPICS}
            stories={stories}
          />

          <div className="naroop-stories-header">
            <h3>
              Community Stories ({filteredStories.length})
              {searchQuery && ` • "${searchQuery}"`}
              {showBookmarksOnly && ' • Bookmarked'}
            </h3>
          </div>

          <div className="naroop-story-list">
            {filteredStories.length === 0 ? (
              <div className="naroop-story-placeholder">
                {searchQuery ? (
                  <p>🔍 No stories found for "{searchQuery}". Try different keywords or browse all stories!</p>
                ) : showBookmarksOnly ? (
                  <p>📚 No bookmarked stories yet. Bookmark your favorite stories to see them here!</p>
                ) : selectedTopic === 'All Stories' ? (
                  <p>✨ Stories from our community will appear here. Share your journey, inspire others, and help us grow together! ✨</p>
                ) : (
                  <p>🔍 No stories found for "{selectedTopic}". Be the first to share a story in this category!</p>
                )}
              </div>
            ) : (
              filteredStories.map(story => (
                <article key={story.id} className="naroop-story">
                  <div className="naroop-story-meta">
                    <span className="naroop-story-topic">{story.topic}</span>
                    <span className="naroop-story-date">{story.timestamp}</span>
                    <span className="naroop-story-author">by {userProfiles[story.author]?.name || 'Community Member'}</span>
                  </div>
                  <h4>{story.title}</h4>
                  <div className="naroop-story-meta-reading">
                    <span className="naroop-reading-time">⏱️ {story.readingTime} min read</span>
                  </div>
                  {story.tags && story.tags.length > 0 && (
                    <div className="naroop-story-tags">
                      {story.tags.map(tag => (
                        <span key={tag} className="naroop-tag">#{tag}</span>
                      ))}
                    </div>
                  )}
                  <p>{story.content}</p>
                  {story.imageUrl && <img src={story.imageUrl} alt="Story visual" className="naroop-story-img" />}
                  <div className="naroop-story-actions">
                    <button
                      className={`naroop-reaction-btn naroop-heart-btn ${hasUserReacted(story, 'heart') ? 'reacted' : ''}`}
                      onClick={() => handleReaction(story.id, 'heart')}
                      title={hasUserReacted(story, 'heart') ? "Remove your love" : "Show love"}
                    >
                      ❤️ {story.hearts || 0}
                    </button>
                    <button
                      className={`naroop-reaction-btn naroop-clap-btn ${hasUserReacted(story, 'clap') ? 'reacted' : ''}`}
                      onClick={() => handleReaction(story.id, 'clap')}
                      title={hasUserReacted(story, 'clap') ? "Remove your applause" : "Applaud this story"}
                    >
                      👏 {story.claps || 0}
                    </button>
                    <button 
                      className="naroop-reaction-btn naroop-share-btn"
                      onClick={() => handleShare(story)}
                      title="Share this story"
                    >
                      📤 {story.shares}
                    </button>
                    <button 
                      className={`naroop-reaction-btn naroop-bookmark-btn ${bookmarkedStories.includes(story.id) ? 'bookmarked' : ''}`}
                      onClick={() => handleBookmark(story.id)}
                      title={bookmarkedStories.includes(story.id) ? "Remove bookmark" : "Bookmark this story"}
                    >
                      {bookmarkedStories.includes(story.id) ? '🔖' : '📚'}
                    </button>
                    {votingPeriod === 'June 2025' && (
                      <button 
                        className="naroop-reaction-btn naroop-vote-btn"
                        onClick={() => handleMonthlyVote(story.id)}
                        title="Vote for Story of the Month"
                      >
                        🗳️ {story.monthlyVotes}
                      </button>
                    )}
                    <button 
                      className="naroop-reaction-btn naroop-export-btn"
                      onClick={() => exportStory(story)}
                      title="Export story"
                    >
                      📄
                    </button>
                    <button
                      className="naroop-reaction-btn naroop-print-btn"
                      onClick={() => printStory(story)}
                      title="Print story"
                    >
                      🖨️
                    </button>
                    {/* Delete button - only show for story author */}
                    {authUser && story.author === authUser.uid && (
                      <>
                        <div className="action-separator" style={{
                          width: '1px',
                          height: '24px',
                          backgroundColor: '#dee2e6',
                          margin: '0 8px'
                        }}></div>
                        <button
                          className="naroop-reaction-btn naroop-delete-btn"
                          onClick={() => handleStoryDelete(story.id, story.title, story.content)}
                          title="Delete your story"
                        >
                          🗑️ Delete
                        </button>
                      </>
                    )}
                  </div>
                </article>
              ))
            )}
          </div>
        </section>
        </div>
      )}

      {/* Common sections for all views - CommunityStats moved to HomeScreen only */}

      <section className="naroop-resources">
        <h3>🤝 Resources & Support</h3>
        <ul>
          {RESOURCES.map(r => (
            <li key={r.url}>
              <a href={r.url} target="_blank" rel="noopener noreferrer">{r.name}</a>
            </li>
          ))}
        </ul>
      </section>

      <section className="naroop-about">
        <h3>About NAROOP</h3>
        <p>
          NAROOP (Narrative of Our People) is dedicated to uplifting Black voices and sharing positive stories, experiences, and resources. We believe in the power of community, unity, and storytelling to inspire and create change.
        </p>
      </section>
      
      <ConfettiCelebration
        trigger={showCelebration}
        message="🏆 New Story of the Month! 🎉"
      />

      {showTaskManagement && (
        <Suspense fallback={<div className="loading-spinner">Loading task management...</div>}>
          <TaskManagementDashboard
            onClose={() => setShowTaskManagement(false)}
          />
        </Suspense>
      )}

      {showMessaging && (
        <Suspense fallback={<div className="loading-spinner">Loading messaging...</div>}>
          <MessagingSystem
            key="messaging-system"
            onClose={() => {
              console.log('Closing messaging system');
              try {
                setShowMessaging(false);
                // Force a small delay to ensure proper state update
                setTimeout(() => {
                  console.log('Messaging system closed successfully');
                }, 100);
              } catch (error) {
                console.error('Error closing messaging:', error);
                // Force close by setting state directly
                setShowMessaging(false);
              }
            }}
          />
        </Suspense>
      )}

      {showNotifications && (
        <Suspense fallback={<div className="loading-spinner">Loading notifications...</div>}>
          <NotificationCenter
            key="notification-center"
            onClose={() => {
              console.log('Closing notification center');
              try {
                setShowNotifications(false);
                // Force a small delay to ensure proper state update
                setTimeout(() => {
                  console.log('Notification center closed successfully');
                }, 100);
              } catch (error) {
                console.error('Error closing notifications:', error);
                // Force close by setting state directly
                setShowNotifications(false);
              }
            }}
            onOpenFriendRequest={handleOpenFriendRequest}
          />
        </Suspense>
      )}

      {showFriendsManager && (
        <Suspense fallback={<div className="loading-spinner">Loading friends...</div>}>
          <FriendsManager
            onClose={() => setShowFriendsManager(false)}
          />
        </Suspense>
      )}

      {showFriendRequestModal && (
        <Suspense fallback={<div className="loading-spinner">Loading friend request...</div>}>
          <FriendRequestModal
            isOpen={showFriendRequestModal}
            onClose={() => {
              setShowFriendRequestModal(false);
              setFriendRequestData(null);
            }}
            requestId={friendRequestData?.requestId}
            fromUserId={friendRequestData?.fromUserId}
            onRequestHandled={handleFriendRequestHandled}
          />
        </Suspense>
      )}

      {showPrivacySettings && (
        <Suspense fallback={<div className="loading-spinner">Loading privacy settings...</div>}>
          <PrivacySettings
            onClose={() => setShowPrivacySettings(false)}
          />
        </Suspense>
      )}

      {showAdminDashboard && (
        <Suspense fallback={<div className="loading-spinner">Loading admin dashboard...</div>}>
          <AdminDashboard
            onClose={() => setShowAdminDashboard(false)}
          />
        </Suspense>
      )}





      {showNewsfeed && (
        <Suspense fallback={<div className="loading-spinner">Loading newsfeed...</div>}>
          <DynamicNewsfeed
            onClose={() => setShowNewsfeed(false)}
          />
        </Suspense>
      )}

      {showChangelog && (
        <Suspense fallback={<div className="loading-spinner">Loading changelog...</div>}>
          <Changelog
            onClose={() => setShowChangelog(false)}
          />
        </Suspense>
      )}

      <ScrollToTop />
      {/* Footer only shows on home page - landing page has its own footer */}
      {currentView === 'home' && <Footer />}


    </main>
    );
  } catch (error) {
    console.error('Error rendering App:', error);
    // Force close all modals in case of error
    setShowMessaging(false);
    setShowNotifications(false);
    setShowTaskManagement(false);
    setShowNewsfeed(false);
    setShowChangelog(false);
    setShowFriendRequestModal(false);
    setFriendRequestData(null);

    return (
      <div style={{ padding: '20px', textAlign: 'center', minHeight: '100vh', background: '#f7fafc' }}>
        <h1>Something went wrong</h1>
        <p>Please refresh the page or contact support.</p>
        <button
          onClick={() => window.location.reload()}
          style={{
            background: '#34d399',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '8px',
            cursor: 'pointer',
            marginTop: '10px'
          }}
        >
          Refresh Page
        </button>
        <details style={{ marginTop: '20px', textAlign: 'left' }}>
          <summary>Error Details</summary>
          <pre style={{ background: '#f0f0f0', padding: '10px', borderRadius: '4px' }}>
            {error.toString()}
          </pre>
        </details>
      </div>
    );
  }
}

export default App
