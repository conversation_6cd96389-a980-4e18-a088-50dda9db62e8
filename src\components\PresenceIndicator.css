/* Presence Indicator Styles */
.presence-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  font-family: inherit;
}

/* Presence dot styles */
.presence-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid var(--color-light);
  flex-shrink: 0;
  position: relative;
}

/* Size variations */
.presence-indicator.small .presence-dot {
  width: 8px;
  height: 8px;
}

.presence-indicator.medium .presence-dot {
  width: 12px;
  height: 12px;
}

.presence-indicator.large .presence-dot {
  width: 16px;
  height: 16px;
}

/* Status colors using NAROOP heritage colors */
.online-dot {
  background-color: var(--color-accent-green);
  box-shadow: 0 0 8px rgba(34, 139, 34, 0.4);
  animation: pulse-online 2s infinite;
}

.away-dot {
  background-color: var(--color-primary);
  box-shadow: 0 0 8px rgba(184, 134, 11, 0.4);
}

.offline-dot {
  background-color: var(--color-gray-medium);
  box-shadow: none;
}

.loading-dot {
  background-color: var(--color-gray-light);
  animation: pulse-loading 1.5s infinite;
}

/* Pulse animations */
@keyframes pulse-online {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes pulse-loading {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}

/* Text styles */
.presence-text {
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--color-dark);
}

.presence-indicator.online .presence-text {
  color: var(--color-accent-green);
}

.presence-indicator.away .presence-text {
  color: var(--color-primary);
}

.presence-indicator.offline .presence-text {
  color: var(--color-gray-medium);
}

.last-seen {
  font-size: var(--text-xs);
  color: var(--color-gray-medium);
  font-style: italic;
  margin-left: var(--space-xs);
}

/* Online Counter Styles */
.online-counter {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  background: linear-gradient(135deg, var(--color-accent-green), var(--color-accent-emerald));
  color: var(--color-light);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
}

.online-counter:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.online-counter.loading {
  background: var(--color-gray-light);
  color: var(--color-gray-medium);
}

.counter-icon {
  font-size: 1.1em;
}

.counter-text {
  white-space: nowrap;
}

/* Online Users List Styles */
.online-users-list {
  background: var(--color-light);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(184, 134, 11, 0.1);
}

.online-users-list.loading,
.online-users-list.empty {
  text-align: center;
  padding: var(--space-xl);
  color: var(--color-gray-medium);
}

.users-header {
  margin-bottom: var(--space-md);
  padding-bottom: var(--space-sm);
  border-bottom: 2px solid var(--color-primary);
}

.users-header h4 {
  margin: 0;
  color: var(--color-primary);
  font-size: var(--text-lg);
  font-weight: 600;
}

.users-grid {
  display: grid;
  gap: var(--space-sm);
  max-height: 300px;
  overflow-y: auto;
}

.online-user-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  background: var(--color-gray-light);
  transition: var(--transition-fast);
  cursor: pointer;
}

.online-user-item:hover {
  background: rgba(184, 134, 11, 0.1);
  transform: translateX(2px);
}

.user-avatar {
  font-size: 1.5em;
  position: relative;
  flex-shrink: 0;
}

.user-avatar .presence-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: var(--color-dark);
  font-size: var(--text-sm);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Presence Status Styles */
.presence-status {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  background: var(--color-light);
  border: 1px solid rgba(184, 134, 11, 0.2);
  transition: var(--transition-normal);
}

.presence-status:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.status-avatar {
  position: relative;
  font-size: 2em;
  flex-shrink: 0;
}

.status-avatar .presence-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  background: var(--color-light);
  border-radius: 50%;
  padding: 1px;
}

.status-info {
  flex: 1;
  min-width: 0;
}

.status-name {
  font-weight: 600;
  color: var(--color-dark);
  font-size: var(--text-base);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .online-users-list {
    padding: var(--space-sm);
  }
  
  .users-grid {
    max-height: 200px;
  }
  
  .online-user-item {
    padding: var(--space-xs);
  }
  
  .user-avatar {
    font-size: 1.2em;
  }
  
  .presence-status {
    padding: var(--space-xs);
  }
  
  .status-avatar {
    font-size: 1.5em;
  }
  
  .online-counter {
    padding: var(--space-xs);
    font-size: var(--text-xs);
  }
}

/* Accessibility improvements */
.presence-indicator[aria-label],
.online-counter[aria-label],
.online-user-item[aria-label] {
  cursor: pointer;
}

.presence-indicator:focus,
.online-counter:focus,
.online-user-item:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .online-dot {
    border: 3px solid var(--color-dark);
  }
  
  .away-dot {
    border: 3px solid var(--color-dark);
  }
  
  .offline-dot {
    border: 3px solid var(--color-dark);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .online-dot,
  .loading-dot {
    animation: none;
  }
  
  .online-counter,
  .online-user-item,
  .presence-status {
    transition: none;
  }
}
