/* Footer Styles */
.naroop-footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  margin-top: var(--space-2xl);
  padding: var(--space-2xl) 0 var(--space-lg) 0;
  border-top: 4px solid var(--color-primary);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-xl);
}

.footer-section h4 {
  color: var(--color-secondary);
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-md);
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: var(--space-sm);
  display: inline-block;
}

.footer-section p {
  margin-bottom: var(--space-sm);
  line-height: 1.6;
}

.footer-mission {
  font-style: italic;
  color: #bdc3c7;
  font-size: var(--text-sm);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-sm);
}

.footer-links a {
  color: #ecf0f1;
  text-decoration: none;
  transition: color var(--transition-fast);
  font-size: var(--text-sm);
}

.footer-links a:hover {
  color: var(--color-secondary);
  text-decoration: underline;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.social-links span {
  font-size: var(--text-sm);
  color: #bdc3c7;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.footer-bottom {
  margin-top: var(--space-xl);
  padding-top: var(--space-lg);
  border-top: 1px solid #34495e;
}

.footer-bottom-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.copyright p {
  margin: 0;
  font-size: var(--text-sm);
  color: #bdc3c7;
}

.footer-tagline {
  font-style: italic;
  color: var(--color-secondary);
  font-weight: 500;
}

.version-info {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.version-badge {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.dev-indicator {
  background: var(--color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    padding: 0 var(--space-md);
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    padding: 0 var(--space-md);
  }

  .social-links {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .social-links span {
    font-size: var(--text-xs);
  }
}

@media (max-width: 480px) {
  .naroop-footer {
    padding: var(--space-xl) 0 var(--space-md) 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-md);
    padding: 0 var(--space-sm);
  }

  .footer-section h4 {
    font-size: var(--text-base);
  }

  .footer-bottom-content {
    padding: 0 var(--space-sm);
  }

  .version-badge {
    font-size: 10px;
    padding: 4px 8px;
  }
}

/* Dark mode support */
.dark-mode .naroop-footer {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-top-color: var(--color-primary);
}

.dark-mode .footer-bottom {
  border-top-color: #404040;
}

.dark-mode .version-badge {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .naroop-footer {
    background: #000;
    color: #fff;
    border-top: 4px solid #fff;
  }

  .footer-links a {
    color: #fff;
  }

  .footer-links a:hover {
    color: #ffff00;
  }

  .version-badge {
    background: #fff;
    color: #000;
    border: 2px solid #fff;
  }
}
