/**
 * NAROOP Color Accessibility Testing Utilities
 * Validates WCAG AA compliance for the Black & Gold color theme
 */

// NAROOP Black & Gold Color Palette
export const NAROOP_COLORS = {
  // Core Foundation Colors
  heritageBlack: '#1A1A1A',
  heritageGold: '#FFD700',
  heritageDeepGold: '#B8860B',
  heritageCream: '#FFF8DC',
  
  // Empowerment Colors
  empowermentAmber: '#FFBF00',
  communityBronze: '#CD7F32',
  wisdomCopper: '#B87333',
  prosperityChampagne: '#F7E7CE',
  
  // Cultural Heritage Accents
  heritageForest: '#355E3B',
  heritageBurgundy: '#800020',
  heritageRoyal: '#4B0082',
  heritageEarth: '#8B4513',
  
  // Supporting Colors
  white: '#FFFFFF',
  lightGray: '#F5F5F5'
};

/**
 * Convert hex color to RGB values
 * @param {string} hex - Hex color code (e.g., '#FFD700')
 * @returns {object} RGB values {r, g, b}
 */
export function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * Calculate relative luminance of a color
 * @param {object} rgb - RGB values {r, g, b}
 * @returns {number} Relative luminance (0-1)
 */
export function getRelativeLuminance(rgb) {
  const { r, g, b } = rgb;
  
  // Convert to sRGB
  const rsRGB = r / 255;
  const gsRGB = g / 255;
  const bsRGB = b / 255;
  
  // Apply gamma correction
  const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
  const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
  const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
  
  // Calculate relative luminance
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
}

/**
 * Calculate contrast ratio between two colors
 * @param {string} color1 - First color (hex)
 * @param {string} color2 - Second color (hex)
 * @returns {number} Contrast ratio (1-21)
 */
export function getContrastRatio(color1, color2) {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const lum1 = getRelativeLuminance(rgb1);
  const lum2 = getRelativeLuminance(rgb2);
  
  const lighter = Math.max(lum1, lum2);
  const darker = Math.min(lum1, lum2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Check if color combination meets WCAG AA standards
 * @param {string} foreground - Foreground color (hex)
 * @param {string} background - Background color (hex)
 * @param {string} level - 'AA' or 'AAA'
 * @param {string} size - 'normal' or 'large'
 * @returns {object} Compliance result
 */
export function checkWCAGCompliance(foreground, background, level = 'AA', size = 'normal') {
  const ratio = getContrastRatio(foreground, background);
  
  let requiredRatio;
  if (level === 'AAA') {
    requiredRatio = size === 'large' ? 4.5 : 7;
  } else { // AA
    requiredRatio = size === 'large' ? 3 : 4.5;
  }
  
  return {
    ratio: Math.round(ratio * 100) / 100,
    requiredRatio,
    passes: ratio >= requiredRatio,
    level,
    size
  };
}

/**
 * Test all NAROOP color combinations for accessibility
 * @returns {object} Complete accessibility report
 */
export function testNAROOPAccessibility() {
  const results = {
    textCombinations: [],
    buttonCombinations: [],
    backgroundCombinations: [],
    summary: {
      totalTests: 0,
      passed: 0,
      failed: 0
    }
  };
  
  // Test primary text combinations
  const textTests = [
    { fg: NAROOP_COLORS.heritageBlack, bg: NAROOP_COLORS.heritageCream, name: 'Black text on cream' },
    { fg: NAROOP_COLORS.heritageBlack, bg: NAROOP_COLORS.white, name: 'Black text on white' },
    { fg: NAROOP_COLORS.heritageCream, bg: NAROOP_COLORS.heritageBlack, name: 'Cream text on black' },
    { fg: NAROOP_COLORS.heritageGold, bg: NAROOP_COLORS.heritageBlack, name: 'Gold text on black' },
    { fg: NAROOP_COLORS.wisdomCopper, bg: NAROOP_COLORS.heritageCream, name: 'Copper text on cream' },
    { fg: NAROOP_COLORS.heritageForest, bg: NAROOP_COLORS.heritageCream, name: 'Forest text on cream' }
  ];
  
  textTests.forEach(test => {
    const result = checkWCAGCompliance(test.fg, test.bg, 'AA', 'normal');
    results.textCombinations.push({
      ...test,
      ...result
    });
    results.summary.totalTests++;
    if (result.passes) results.summary.passed++;
    else results.summary.failed++;
  });
  
  // Test button combinations
  const buttonTests = [
    { fg: NAROOP_COLORS.heritageGold, bg: 'transparent', border: NAROOP_COLORS.heritageGold, name: 'Gold button outline' },
    { fg: NAROOP_COLORS.heritageBlack, bg: NAROOP_COLORS.heritageGold, name: 'Gold button filled' },
    { fg: NAROOP_COLORS.heritageDeepGold, bg: 'transparent', border: NAROOP_COLORS.heritageDeepGold, name: 'Deep gold button outline' },
    { fg: NAROOP_COLORS.heritageCream, bg: NAROOP_COLORS.heritageDeepGold, name: 'Deep gold button filled' }
  ];
  
  buttonTests.forEach(test => {
    if (test.bg !== 'transparent') {
      const result = checkWCAGCompliance(test.fg, test.bg, 'AA', 'normal');
      results.buttonCombinations.push({
        ...test,
        ...result
      });
      results.summary.totalTests++;
      if (result.passes) results.summary.passed++;
      else results.summary.failed++;
    }
  });
  
  return results;
}

/**
 * Generate accessibility report for console logging
 * @returns {string} Formatted report
 */
export function generateAccessibilityReport() {
  const results = testNAROOPAccessibility();
  
  let report = '\n=== NAROOP Black & Gold Accessibility Report ===\n\n';
  
  report += 'TEXT COMBINATIONS:\n';
  results.textCombinations.forEach(test => {
    const status = test.passes ? '✅ PASS' : '❌ FAIL';
    report += `${status} ${test.name}: ${test.ratio}:1 (required: ${test.requiredRatio}:1)\n`;
  });
  
  report += '\nBUTTON COMBINATIONS:\n';
  results.buttonCombinations.forEach(test => {
    const status = test.passes ? '✅ PASS' : '❌ FAIL';
    report += `${status} ${test.name}: ${test.ratio}:1 (required: ${test.requiredRatio}:1)\n`;
  });
  
  report += `\nSUMMARY:\n`;
  report += `Total Tests: ${results.summary.totalTests}\n`;
  report += `Passed: ${results.summary.passed}\n`;
  report += `Failed: ${results.summary.failed}\n`;
  report += `Success Rate: ${Math.round((results.summary.passed / results.summary.totalTests) * 100)}%\n`;
  
  return report;
}

/**
 * Color blindness simulation (simplified)
 * @param {string} hex - Original hex color
 * @param {string} type - 'protanopia', 'deuteranopia', or 'tritanopia'
 * @returns {string} Simulated hex color
 */
export function simulateColorBlindness(hex, type) {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  
  let { r, g, b } = rgb;
  
  // Simplified color blindness simulation
  switch (type) {
    case 'protanopia': // Red-blind
      r = 0.567 * r + 0.433 * g;
      g = 0.558 * r + 0.442 * g;
      break;
    case 'deuteranopia': // Green-blind
      r = 0.625 * r + 0.375 * g;
      g = 0.7 * r + 0.3 * g;
      break;
    case 'tritanopia': // Blue-blind
      r = 0.95 * r + 0.05 * b;
      b = 0.433 * g + 0.567 * b;
      break;
    default:
      return hex;
  }
  
  // Convert back to hex
  r = Math.round(Math.max(0, Math.min(255, r)));
  g = Math.round(Math.max(0, Math.min(255, g)));
  b = Math.round(Math.max(0, Math.min(255, b)));
  
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

// Export for testing in browser console
if (typeof window !== 'undefined') {
  window.NAROOPAccessibility = {
    testColors: testNAROOPAccessibility,
    generateReport: generateAccessibilityReport,
    checkCompliance: checkWCAGCompliance,
    colors: NAROOP_COLORS
  };
}
